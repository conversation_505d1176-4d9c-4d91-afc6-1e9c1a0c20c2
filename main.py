import json
import time
import uuid
from typing import Dict, Any, Optional

class MeituanRPCClient:
    """美团RPC客户端 - 模拟版本"""

    def __init__(self):
        self.client_id = str(uuid.uuid4())
        print(f"🔧 初始化美团RPC客户端: {self.client_id}")
        print("💡 注意: 这是模拟版本，实际使用需要浏览器中的JavaScript配合")

    def _simulate_browser_call(self, action: str, params: Optional[Dict] = None) -> Dict[str, Any]:
        """模拟浏览器RPC调用"""
        request_id = str(uuid.uuid4())

        print(f"🔄 模拟浏览器调用: {action}")
        print(f"📦 请求参数: {json.dumps(params, ensure_ascii=False, indent=2)}")

        # 模拟浏览器响应
        return self._simulate_response(action, params)

    def _simulate_response(self, action: str, params: Optional[Dict]) -> Dict[str, Any]:
        """模拟RPC响应"""
        timestamp = int(time.time() * 1000)

        if action == 'health':
            return {
                'code': 200,
                'message': 'success',
                'data': {
                    'status': 'healthy',
                    'h5guard_loaded': True,
                    'ready': True,
                    'timestamp': timestamp
                },
                'timestamp': timestamp
            }
        elif action == 'status':
            return {
                'code': 200,
                'message': 'success',
                'data': {
                    'service': 'MeituanRPC',
                    'version': '1.0.0',
                    'ready': True,
                    'h5guard_loaded': True,
                    'uptime': timestamp,
                    'endpoints': ['generateMtgsig', 'health', 'status']
                },
                'timestamp': timestamp
            }
        elif action == 'generateMtgsig':
            # 模拟生成的mtgsig
            mock_mtgsig = f"mock_mtgsig_{int(time.time())}_abcd1234"
            url = params.get('url', '') if params else ''
            method = params.get('method', '') if params else ''
            data = params.get('data', {}) if params else {}

            return {
                'code': 200,
                'message': 'success',
                'data': {
                    'mtgsig': mock_mtgsig,
                    'url': url,
                    'method': method,
                    'data': data,
                    'headers': {
                        'mtgsig': mock_mtgsig
                    },
                    'timestamp': timestamp
                },
                'timestamp': timestamp
            }
        else:
            return {
                'code': 404,
                'message': f'不支持的操作: {action}',
                'data': None,
                'timestamp': timestamp
            }

    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        return self._simulate_browser_call('health')

    def get_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        return self._simulate_browser_call('status')

    def generate_mtgsig(self, url: str, method: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """生成mtgsig签名"""
        params = {
            'url': url,
            'method': method.upper(),
            'data': data or {}
        }
        return self._simulate_browser_call('generateMtgsig', params)

    def get_mtgsig_only(self, url: str, method: str, data: Optional[Dict] = None) -> str:
        """只返回mtgsig字符串"""
        result = self.generate_mtgsig(url, method, data)
        if result['code'] == 200:
            return result['data']['mtgsig']
        else:
            raise Exception(f"生成mtgsig失败: {result['message']}")

    def test_browser_integration(self):
        """测试浏览器集成说明"""
        print("\n" + "="*60)
        print("🌐 浏览器集成测试说明")
        print("="*60)
        print("1. 在美团页面打开浏览器控制台")
        print("2. 运行提供的JavaScript RPC服务代码")
        print("3. 使用以下命令测试:")
        print()
        print("   // 测试健康检查")
        print("   await window.MeituanRPC.health()")
        print()
        print("   // 生成mtgsig")
        print("   await getMtgsig('/api/path', 'POST', {data: 'value'})")
        print()
        print("   // 完整调用")
        print("   await window.MeituanRPC.generateMtgsig('/api/path', 'POST', {data})")
        print("="*60)

def test_meituan_rpc():
    """测试美团RPC服务"""
    print("🚀 开始测试美团RPC服务\n")
    
    # 创建客户端
    client = MeituanRPCClient()
    
    # 1. 健康检查
    print("1️⃣ 健康检查测试")
    health_result = client.health_check()
    print(f"✅ 健康检查结果: {health_result}")
    print()
    
    # 2. 服务状态
    print("2️⃣ 服务状态测试")
    status_result = client.get_status()
    print(f"📊 服务状态: {status_result}")
    print()
    
    # 3. 生成mtgsig - 发送验证码
    print("3️⃣ 生成mtgsig测试 - 发送验证码")
    sms_result = client.generate_mtgsig(
        url='/s/gateway/login/h5/login/sendLoginFreeSmsCode',
        method='POST',
        data={
            'mobile': '13800138000',
            'countrycode': '86'
        }
    )
    print(f"📱 发送验证码签名: {json.dumps(sms_result, ensure_ascii=False, indent=2)}")
    print()
    
    # 4. 生成mtgsig - 验证码登录
    print("4️⃣ 生成mtgsig测试 - 验证码登录")
    login_result = client.generate_mtgsig(
        url='/s/gateway/login/h5/login/loginBySmsCode',
        method='POST',
        data={
            'mobile': '13800138000',
            'smsCode': '123456',
            'countrycode': '86'
        }
    )
    print(f"🔐 验证码登录签名: {json.dumps(login_result, ensure_ascii=False, indent=2)}")
    print()
    
    # 5. 只获取mtgsig字符串
    print("5️⃣ 快速获取mtgsig字符串")
    try:
        mtgsig_only = client.get_mtgsig_only(
            url='/s/gateway/user/h5/userInfo',
            method='GET'
        )
        print(f"🔑 mtgsig: {mtgsig_only}")
    except Exception as e:
        print(f"❌ 获取失败: {e}")
    print()
    
    print("✅ 测试完成！")

def test_batch_requests():
    """批量请求测试"""
    print("🔄 批量请求测试\n")
    
    client = MeituanRPCClient()
    
    # 测试多个API的签名生成
    test_apis = [
        {
            'name': '发送验证码',
            'url': '/s/gateway/login/h5/login/sendLoginFreeSmsCode',
            'method': 'POST',
            'data': {'mobile': '13800138000', 'countrycode': '86'}
        },
        {
            'name': '用户信息',
            'url': '/s/gateway/user/h5/userInfo',
            'method': 'GET',
            'data': {}
        },
        {
            'name': '订单列表',
            'url': '/s/gateway/order/h5/orderList',
            'method': 'POST',
            'data': {'page': 1, 'size': 10}
        }
    ]
    
    for i, api in enumerate(test_apis, 1):
        print(f"{i}️⃣ 测试 {api['name']}")
        result = client.generate_mtgsig(api['url'], api['method'], api['data'])
        
        if result['code'] == 200:
            print(f"✅ 成功: {result['data']['mtgsig']}")
        else:
            print(f"❌ 失败: {result['message']}")
        print()

if __name__ == "__main__":
    # 运行基础测试
    test_meituan_rpc()
    
    print("\n" + "="*50 + "\n")
    
    # 运行批量测试
    test_batch_requests()