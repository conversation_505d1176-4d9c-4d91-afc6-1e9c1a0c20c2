#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
美团RPC API使用示例
演示如何在Python项目中使用美团RPC签名生成API
"""

import requests
import json
from typing import Dict, Any, Optional


class MeituanSignatureClient:
    """美团签名客户端 - 简化版"""
    
    def __init__(self, api_url: str = "http://localhost:3000"):
        """
        初始化客户端
        
        Args:
            api_url: API服务地址
        """
        self.api_url = api_url.rstrip('/')
    
    def generate_signature(self, url: str, method: str = "POST", data: Optional[Dict[str, Any]] = None) -> str:
        """
        生成美团API签名
        
        Args:
            url: 美团API路径
            method: HTTP方法
            data: 请求数据
            
        Returns:
            mtgsig签名字符串
            
        Raises:
            Exception: 当签名生成失败时
        """
        payload = {
            "url": url,
            "method": method.upper(),
            "data": data or {}
        }
        
        try:
            response = requests.post(
                f"{self.api_url}/api/generate-signature",
                json=payload,
                timeout=30
            )
            response.raise_for_status()
            
            result = response.json()
            if result.get("success"):
                return result.get("mtgsig")
            else:
                raise Exception(f"签名生成失败: {result.get('error')}")
                
        except requests.exceptions.RequestException as e:
            raise Exception(f"API请求失败: {e}")
    
    def check_service_status(self) -> bool:
        """
        检查服务状态
        
        Returns:
            服务是否可用
        """
        try:
            response = requests.get(f"{self.api_url}/api/status", timeout=5)
            response.raise_for_status()
            status = response.json()
            return status.get("connected", False)
        except:
            return False


def example_basic_usage():
    """基本使用示例"""
    print("📝 基本使用示例")
    print("-" * 30)
    
    # 创建客户端
    client = MeituanSignatureClient()
    
    # 检查服务状态
    if not client.check_service_status():
        print("❌ API服务不可用，请确保:")
        print("  1. API服务正在运行 (npm start)")
        print("  2. 浏览器客户端已注入到美团页面")
        return
    
    try:
        # 生成签名示例
        mtgsig = client.generate_signature(
            url="/s/gateway/user/h5/userInfo",
            method="GET"
        )
        print(f"✅ 签名生成成功: {mtgsig[:30]}...")
        
    except Exception as e:
        print(f"❌ 签名生成失败: {e}")


def example_login_sms():
    """发送登录短信示例"""
    print("\n📱 发送登录短信示例")
    print("-" * 30)
    
    client = MeituanSignatureClient()
    
    if not client.check_service_status():
        print("❌ API服务不可用")
        return
    
    try:
        # 发送登录短信的签名
        mtgsig = client.generate_signature(
            url="/s/gateway/login/h5/login/sendLoginFreeSmsCode",
            method="POST",
            data={
                "mobile": "13800138000",  # 测试手机号
                "countrycode": "86"
            }
        )
        print(f"✅ 短信签名生成成功: {mtgsig[:30]}...")
        
        # 这里可以继续调用美团的实际API
        # headers = {"mtgsig": mtgsig}
        # response = requests.post("https://sqt.meituan.com/s/gateway/login/h5/login/sendLoginFreeSmsCode", ...)
        
    except Exception as e:
        print(f"❌ 短信签名生成失败: {e}")


def example_batch_signatures():
    """批量生成签名示例"""
    print("\n🔄 批量生成签名示例")
    print("-" * 30)
    
    client = MeituanSignatureClient()
    
    if not client.check_service_status():
        print("❌ API服务不可用")
        return
    
    # 需要签名的API列表
    api_list = [
        {"url": "/s/gateway/user/h5/userInfo", "method": "GET"},
        {"url": "/s/gateway/order/h5/orderList", "method": "POST", "data": {"page": 1}},
        {"url": "/s/gateway/wallet/h5/balance", "method": "GET"},
    ]
    
    print(f"正在为 {len(api_list)} 个API生成签名...")
    
    signatures = {}
    for i, api in enumerate(api_list, 1):
        try:
            mtgsig = client.generate_signature(**api)
            signatures[api["url"]] = mtgsig
            print(f"  {i}. {api['url']}: ✅ 成功")
        except Exception as e:
            print(f"  {i}. {api['url']}: ❌ 失败 ({e})")
    
    print(f"\n✅ 批量签名完成，成功生成 {len(signatures)} 个签名")


def example_with_error_handling():
    """带错误处理的示例"""
    print("\n🛡️ 错误处理示例")
    print("-" * 30)
    
    client = MeituanSignatureClient()
    
    def safe_generate_signature(url: str, method: str = "POST", data: Optional[Dict] = None, max_retries: int = 3) -> Optional[str]:
        """
        安全的签名生成（带重试机制）
        
        Args:
            url: API路径
            method: HTTP方法
            data: 请求数据
            max_retries: 最大重试次数
            
        Returns:
            签名字符串或None
        """
        for attempt in range(max_retries):
            try:
                return client.generate_signature(url, method, data)
            except Exception as e:
                print(f"  尝试 {attempt + 1}/{max_retries} 失败: {e}")
                if attempt == max_retries - 1:
                    print(f"  ❌ 所有重试都失败了")
                    return None
        return None
    
    # 测试重试机制
    print("测试重试机制...")
    mtgsig = safe_generate_signature("/api/test", "GET", max_retries=2)
    if mtgsig:
        print(f"✅ 最终成功: {mtgsig[:30]}...")
    else:
        print("❌ 最终失败")


def main():
    """主函数"""
    print("🚀 美团RPC API Python使用示例")
    print("=" * 50)
    
    # 运行各种示例
    example_basic_usage()
    example_login_sms()
    example_batch_signatures()
    example_with_error_handling()
    
    print("\n" + "=" * 50)
    print("🎉 示例演示完成！")
    print("\n💡 在实际项目中使用时:")
    print("  1. 将MeituanSignatureClient类复制到你的项目中")
    print("  2. 根据需要调整API地址和错误处理逻辑")
    print("  3. 确保API服务和浏览器客户端正常运行")


if __name__ == "__main__":
    main()
