#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
美团RPC签名生成API测试脚本
用于测试美团RPC签名生成API服务的各个接口
"""

import requests
import json
import time
import asyncio
import aiohttp
from typing import Dict, Any, Optional, List
from datetime import datetime


class MeituanAPIClient:
    """美团API客户端"""
    
    def __init__(self, base_url: str = "http://localhost:3000"):
        """
        初始化API客户端
        
        Args:
            base_url: API服务基础URL
        """
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'MeituanAPIClient/1.0'
        })
    
    def generate_signature(self, url: str, method: str, data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        生成签名
        
        Args:
            url: 请求URL路径
            method: HTTP方法
            data: 请求数据
            
        Returns:
            签名生成结果
            
        Raises:
            Exception: 当签名生成失败时
        """
        payload = {
            "url": url,
            "method": method.upper(),
            "data": data or {}
        }
        
        try:
            response = self.session.post(f"{self.base_url}/api/generate-signature", json=payload)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            if hasattr(e, 'response') and e.response is not None:
                error_msg = e.response.json().get('error', str(e))
            else:
                error_msg = str(e)
            raise Exception(f"签名生成失败: {error_msg}")
    
    def get_history(self, limit: int = 10) -> Dict[str, Any]:
        """
        获取调用历史
        
        Args:
            limit: 返回记录数量限制
            
        Returns:
            调用历史数据
        """
        try:
            response = self.session.get(f"{self.base_url}/api/history", params={"limit": limit})
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            raise Exception(f"获取历史失败: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取服务状态
        
        Returns:
            服务状态信息
        """
        try:
            response = self.session.get(f"{self.base_url}/api/status")
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            raise Exception(f"获取状态失败: {e}")
    
    def clear_history(self) -> Dict[str, Any]:
        """
        清空调用历史
        
        Returns:
            清空结果
        """
        try:
            response = self.session.delete(f"{self.base_url}/api/history")
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            raise Exception(f"清空历史失败: {e}")
    
    def close(self):
        """关闭会话"""
        self.session.close()


class AsyncMeituanAPIClient:
    """异步美团API客户端"""
    
    def __init__(self, base_url: str = "http://localhost:3000"):
        self.base_url = base_url.rstrip('/')
    
    async def generate_signature(self, url: str, method: str, data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """异步生成签名"""
        payload = {
            "url": url,
            "method": method.upper(),
            "data": data or {}
        }
        
        async with aiohttp.ClientSession() as session:
            try:
                async with session.post(f"{self.base_url}/api/generate-signature", json=payload) as response:
                    response.raise_for_status()
                    return await response.json()
            except aiohttp.ClientError as e:
                raise Exception(f"异步签名生成失败: {e}")


def print_separator(title: str):
    """打印分隔符"""
    print(f"\n{'='*50}")
    print(f" {title}")
    print('='*50)


def print_result(title: str, data: Any, success: bool = True):
    """打印结果"""
    status = "✅" if success else "❌"
    print(f"\n{status} {title}:")
    if isinstance(data, dict):
        print(json.dumps(data, indent=2, ensure_ascii=False))
    else:
        print(data)


def test_basic_functionality():
    """测试基本功能"""
    print_separator("基本功能测试")
    
    client = MeituanAPIClient()
    
    try:
        # 1. 检查服务状态
        print("1. 检查服务状态...")
        status = client.get_status()
        print_result("服务状态", {
            "connected": status.get("connected"),
            "pendingRequests": status.get("pendingRequests"),
            "uptime": f"{status.get('uptime', 0) // 1000}秒",
            "totalCalls": status.get("statistics", {}).get("totalCalls", 0)
        })
        
        if not status.get("connected"):
            print("\n⚠️ 浏览器客户端未连接，请先在美团页面注入browser-client.js")
            return False
        
        # 2. 测试签名生成
        print("\n2. 测试签名生成...")
        test_data = {
            "testKey": "testValue",
            "timestamp": int(time.time()),
            "random": "test123"
        }
        
        sign_result = client.generate_signature("/api/test", "POST", test_data)
        print_result("签名生成", {
            "success": sign_result.get("success"),
            "mtgsig": sign_result.get("mtgsig", "")[:30] + "..." if sign_result.get("mtgsig") else None,
            "requestId": sign_result.get("requestId"),
            "responseTime": f"{sign_result.get('responseTime', 0)}ms"
        })
        
        # 3. 测试不同HTTP方法
        print("\n3. 测试不同HTTP方法...")
        methods = ["GET", "POST", "PUT", "DELETE"]
        for method in methods:
            try:
                result = client.generate_signature(f"/api/test/{method.lower()}", method, {"method": method})
                print(f"  {method}: ✅ 成功 (mtgsig: {result.get('mtgsig', '')[:20]}...)")
            except Exception as e:
                print(f"  {method}: ❌ 失败 ({e})")
        
        # 4. 查看调用历史
        print("\n4. 查看调用历史...")
        history = client.get_history(5)
        print_result("调用历史", {
            "totalRecords": history.get("totalRecords"),
            "recentCalls": len(history.get("history", [])),
            "statistics": history.get("statistics")
        })
        
        return True
        
    except Exception as e:
        print_result("测试失败", str(e), False)
        return False
    finally:
        client.close()


def test_batch_requests():
    """测试批量请求"""
    print_separator("批量请求测试")
    
    client = MeituanAPIClient()
    
    try:
        print("发送10个并发请求...")
        start_time = time.time()
        
        # 模拟批量请求
        results = []
        for i in range(10):
            try:
                result = client.generate_signature(
                    f"/api/batch/{i}",
                    "POST",
                    {"index": i, "timestamp": int(time.time())}
                )
                results.append(result)
                print(f"  请求 {i+1}: ✅ 成功")
            except Exception as e:
                print(f"  请求 {i+1}: ❌ 失败 ({e})")
        
        end_time = time.time()
        success_count = len([r for r in results if r.get("success")])
        
        print_result("批量请求结果", {
            "总请求数": 10,
            "成功数": success_count,
            "失败数": 10 - success_count,
            "成功率": f"{success_count/10*100:.1f}%",
            "总耗时": f"{end_time - start_time:.2f}秒"
        })
        
    except Exception as e:
        print_result("批量测试失败", str(e), False)
    finally:
        client.close()


async def test_async_requests():
    """测试异步请求"""
    print_separator("异步请求测试")
    
    client = AsyncMeituanAPIClient()
    
    try:
        print("发送5个异步并发请求...")
        start_time = time.time()
        
        # 创建异步任务
        tasks = []
        for i in range(5):
            task = client.generate_signature(
                f"/api/async/{i}",
                "POST",
                {"index": i, "async": True, "timestamp": int(time.time())}
            )
            tasks.append(task)
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        end_time = time.time()
        success_count = len([r for r in results if isinstance(r, dict) and r.get("success")])
        
        print_result("异步请求结果", {
            "总请求数": 5,
            "成功数": success_count,
            "失败数": 5 - success_count,
            "成功率": f"{success_count/5*100:.1f}%",
            "总耗时": f"{end_time - start_time:.2f}秒"
        })
        
        # 显示每个请求的结果
        for i, result in enumerate(results):
            if isinstance(result, dict):
                print(f"  异步请求 {i+1}: ✅ 成功 (响应时间: {result.get('responseTime', 0)}ms)")
            else:
                print(f"  异步请求 {i+1}: ❌ 失败 ({result})")
        
    except Exception as e:
        print_result("异步测试失败", str(e), False)


def test_error_handling():
    """测试错误处理"""
    print_separator("错误处理测试")
    
    client = MeituanAPIClient()
    
    try:
        # 1. 测试无效参数
        print("1. 测试无效参数...")
        try:
            client.generate_signature("", "", {})
            print("  ❌ 应该失败但成功了")
        except Exception as e:
            print(f"  ✅ 正确捕获错误: {e}")
        
        # 2. 测试超大数据
        print("\n2. 测试超大数据...")
        large_data = {"data": "x" * 10000}  # 10KB数据
        try:
            result = client.generate_signature("/api/large", "POST", large_data)
            print(f"  ✅ 大数据处理成功: {result.get('success')}")
        except Exception as e:
            print(f"  ⚠️ 大数据处理失败: {e}")
        
    except Exception as e:
        print_result("错误处理测试失败", str(e), False)
    finally:
        client.close()


def main():
    """主测试函数"""
    print("🧪 美团RPC API接口测试开始")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 基本功能测试
    if not test_basic_functionality():
        print("\n❌ 基本功能测试失败，跳过其他测试")
        return
    
    # 批量请求测试
    test_batch_requests()
    
    # 异步请求测试
    asyncio.run(test_async_requests())
    
    # 错误处理测试
    test_error_handling()
    
    print_separator("测试完成")
    print("🎉 所有测试已完成！")
    print("\n💡 提示:")
    print("  - 如果测试失败，请确保API服务正在运行")
    print("  - 确保浏览器客户端已在美团页面注入")
    print("  - 检查网络连接是否正常")


if __name__ == "__main__":
    main()
