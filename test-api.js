/**
 * API测试脚本
 * 用于测试美团RPC签名生成API服务
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000';

class MeituanAPIClient {
    constructor(baseUrl = API_BASE_URL) {
        this.baseUrl = baseUrl;
    }

    // 生成签名
    async generateSignature(url, method, data = {}) {
        try {
            const response = await axios.post(`${this.baseUrl}/api/generate-signature`, {
                url,
                method,
                data
            });
            return response.data;
        } catch (error) {
            throw new Error(`签名生成失败: ${error.response?.data?.error || error.message}`);
        }
    }

    // 获取调用历史
    async getHistory(limit = 10) {
        try {
            const response = await axios.get(`${this.baseUrl}/api/history?limit=${limit}`);
            return response.data;
        } catch (error) {
            throw new Error(`获取历史失败: ${error.message}`);
        }
    }

    // 获取服务状态
    async getStatus() {
        try {
            const response = await axios.get(`${this.baseUrl}/api/status`);
            return response.data;
        } catch (error) {
            throw new Error(`获取状态失败: ${error.message}`);
        }
    }

    // 清空历史记录
    async clearHistory() {
        try {
            const response = await axios.delete(`${this.baseUrl}/api/history`);
            return response.data;
        } catch (error) {
            throw new Error(`清空历史失败: ${error.message}`);
        }
    }
}

// 测试函数
async function runTests() {
    const client = new MeituanAPIClient();

    console.log('🧪 开始API测试...\n');

    try {
        // 1. 检查服务状态
        console.log('1. 检查服务状态...');
        const status = await client.getStatus();
        console.log('✅ 服务状态:', {
            connected: status.connected,
            pendingRequests: status.pendingRequests,
            uptime: Math.round(status.uptime / 1000) + 's'
        });

        if (!status.connected) {
            console.log('⚠️ 浏览器客户端未连接，请先在美团页面注入browser-client.js');
            return;
        }

        // 2. 测试签名生成
        console.log('\n2. 测试签名生成...');
        const signResult = await client.generateSignature(
            '/api/test',
            'POST',
            { testKey: 'testValue', timestamp: Date.now() }
        );
        console.log('✅ 签名生成成功:', {
            success: signResult.success,
            mtgsig: signResult.mtgsig?.substring(0, 20) + '...',
            requestId: signResult.requestId
        });

        // 3. 测试多个请求
        console.log('\n3. 测试批量请求...');
        const batchPromises = [];
        for (let i = 0; i < 3; i++) {
            batchPromises.push(
                client.generateSignature(`/api/batch/${i}`, 'GET', { index: i })
            );
        }
        const batchResults = await Promise.all(batchPromises);
        console.log(`✅ 批量请求完成: ${batchResults.filter(r => r.success).length}/3 成功`);

        // 4. 查看调用历史
        console.log('\n4. 查看调用历史...');
        const history = await client.getHistory(5);
        console.log('✅ 历史记录:', {
            totalRecords: history.totalRecords,
            recentCalls: history.history.length,
            statistics: history.statistics
        });

        console.log('\n🎉 所有测试完成！');

    } catch (error) {
        console.error('❌ 测试失败:', error.message);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    runTests();
}

module.exports = { MeituanAPIClient, runTests };
