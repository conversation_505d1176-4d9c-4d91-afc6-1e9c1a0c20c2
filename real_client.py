#!/usr/bin/env python3
"""
美团RPC真实客户端
通过与浏览器JavaScript服务通信来获取真实的mtgsig签名
不包含任何模拟数据，需要与浏览器中的RPC服务配合使用
"""

import json
from typing import Dict, Any, Optional

class MeituanRPCClient:
    """美团RPC客户端 - 与浏览器JavaScript服务通信"""

    def __init__(self):
        print("🔧 初始化美团RPC客户端")
        print("⚠️  重要: 此客户端需要与浏览器中的JavaScript RPC服务配合使用")
        print("📋 功能: 发送验证码签名 + 通用签名生成")
        print("🌐 请确保已在美团页面浏览器控制台中运行RPC服务")

    def send_sms_code_signature(self, mobile: str, countrycode: str = '86') -> None:
        """
        生成发送验证码的签名
        需要在浏览器控制台中手动执行
        """
        print(f"\n📱 生成发送验证码签名")
        print(f"📞 手机号: {mobile}")
        print(f"🌍 国家代码: {countrycode}")
        print(f"\n💻 请在美团页面浏览器控制台中运行以下命令:")
        print(f"   await window.MeituanRPC.sendSMSCode('{mobile}', '{countrycode}');")
        print(f"\n📋 命令执行后，您将在控制台看到真实的mtgsig签名结果")

    def generate_mtgsig(self, url: str, method: str, data: Optional[Dict] = None) -> None:
        """
        生成通用mtgsig签名
        需要在浏览器控制台中手动执行
        """
        print(f"\n🔐 生成通用mtgsig签名")
        print(f"🔗 URL: {url}")
        print(f"📡 方法: {method}")
        if data:
            print(f"📦 数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
        
        print(f"\n💻 请在美团页面浏览器控制台中运行以下命令:")
        data_str = json.dumps(data) if data else '{}'
        print(f"   await window.MeituanRPC.generateMtgsig('{url}', '{method}', {data_str});")
        print(f"\n📋 命令执行后，您将在控制台看到真实的mtgsig签名结果")

    def quick_mtgsig(self, url: str, method: str, data: Optional[Dict] = None) -> None:
        """
        快速获取mtgsig字符串
        需要在浏览器控制台中手动执行
        """
        print(f"\n⚡ 快速获取mtgsig字符串")
        print(f"🔗 URL: {url}")
        print(f"📡 方法: {method}")
        if data:
            print(f"📦 数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
        
        print(f"\n💻 请在美团页面浏览器控制台中运行以下命令:")
        data_str = json.dumps(data) if data else '{}'
        print(f"   await getMtgsig('{url}', '{method}', {data_str});")
        print(f"\n📋 命令执行后，您将在控制台看到mtgsig字符串")

    def run_test_suite(self) -> None:
        """
        运行完整的测试套件
        需要在浏览器控制台中手动执行
        """
        print(f"\n🧪 运行完整测试套件")
        print(f"\n💻 请在美团页面浏览器控制台中运行以下命令:")
        print(f"   await window.MeituanRPC.testSignature();")
        print(f"\n📋 命令执行后，您将看到完整的测试结果，包括:")
        print(f"   - 发送验证码API签名测试")
        print(f"   - 用户信息API签名测试")
        print(f"   - 测试结果汇总")

    def browser_setup_guide(self) -> None:
        """显示浏览器设置指南"""
        print("\n" + "="*60)
        print("🌐 浏览器设置指南")
        print("="*60)
        print("1. 打开美团页面: https://sqt.meituan.com/")
        print("2. 打开浏览器开发者工具 (F12)")
        print("3. 切换到 Console 标签")
        print("4. 复制并运行 meituan_rpc_service.js 中的代码")
        print("5. 等待看到 '✅ 美团真实RPC服务已启动' 消息")
        print("6. 然后使用本Python客户端提供的命令")
        print("="*60)

    def usage_examples(self) -> None:
        """显示使用示例"""
        print("\n" + "="*60)
        print("📖 使用示例")
        print("="*60)
        print("# Python端调用:")
        print("client = MeituanRPCClient()")
        print()
        print("# 1. 生成发送验证码签名")
        print("client.send_sms_code_signature('13800138000', '86')")
        print()
        print("# 2. 生成通用API签名")
        print("client.generate_mtgsig('/s/gateway/user/h5/userInfo', 'GET')")
        print()
        print("# 3. 快速获取mtgsig")
        print("client.quick_mtgsig('/api/path', 'POST', {'key': 'value'})")
        print()
        print("# 4. 运行完整测试")
        print("client.run_test_suite()")
        print("="*60)

def test_meituan_rpc():
    """测试美团RPC服务"""
    print("🚀 美团RPC服务测试")
    print("="*50)
    
    # 创建客户端
    client = MeituanRPCClient()
    
    # 显示浏览器设置指南
    client.browser_setup_guide()
    
    # 显示使用示例
    client.usage_examples()
    
    print("\n🧪 开始测试流程:")
    print("-" * 30)
    
    # 1. 发送验证码签名测试
    print("\n1️⃣ 发送验证码签名测试")
    client.send_sms_code_signature('13800138000', '86')
    
    # 2. 通用签名生成测试
    print("\n2️⃣ 通用签名生成测试")
    client.generate_mtgsig('/s/gateway/user/h5/userInfo', 'GET')
    
    # 3. 快速获取mtgsig测试
    print("\n3️⃣ 快速获取mtgsig测试")
    client.quick_mtgsig('/s/gateway/order/h5/orderList', 'POST', {'page': 1, 'size': 10})
    
    # 4. 运行完整测试套件
    print("\n4️⃣ 完整测试套件")
    client.run_test_suite()
    
    print("\n✅ 测试指令已生成完毕！")
    print("💡 请按照上述指令在浏览器控制台中执行，获取真实的mtgsig签名")

if __name__ == "__main__":
    test_meituan_rpc()
