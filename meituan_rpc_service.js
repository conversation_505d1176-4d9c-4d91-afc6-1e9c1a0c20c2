/**
 * 美团RPC签名生成服务
 * 在美团页面浏览器控制台中运行此代码
 * 
 * 使用方法:
 * 1. 打开 https://sqt.meituan.com/
 * 2. 打开浏览器控制台 (F12)
 * 3. 复制粘贴此代码并运行
 * 4. 使用 await window.MeituanRPC.sendSMSCode('手机号', '86') 测试
 */

// 等待H5guard加载的函数
function waitForH5guard(timeout = 10000) {
    return new Promise((resolve, reject) => {
        if (window.H5guard) {
            resolve(window.H5guard);
            return;
        }

        const startTime = Date.now();
        const checkInterval = setInterval(() => {
            if (window.H5guard) {
                clearInterval(checkInterval);
                resolve(window.H5guard);
            } else if (Date.now() - startTime > timeout) {
                clearInterval(checkInterval);
                reject(new Error('H5guard加载超时'));
            }
        }, 100);
    });
}

// 美团RPC服务类
class MeituanRealRPC {
    constructor() {
        this.isReady = true;
        this.requestCount = 0;
        this.init();
    }
    
    init() {
        // 创建全局RPC接口
        window.MeituanRPC = {
            generateMtgsig: this.generateMtgsig.bind(this),
            sendSMSCode: this.sendSMSCode.bind(this),
            testSignature: this.testSignature.bind(this)
        };
        
        // 便捷方法
        window.getMtgsig = this.getMtgsig.bind(this);
        
        console.log('✅ 美团真实RPC服务已启动');
        console.log('📋 可用方法:');
        console.log('  - window.MeituanRPC.generateMtgsig(url, method, data)');
        console.log('  - window.MeituanRPC.sendSMSCode(mobile, countrycode)');
        console.log('  - window.MeituanRPC.testSignature()');
        console.log('  - window.getMtgsig(url, method, data) // 快捷方法');
    }
    
    async generateMtgsig(url, method, data = {}) {
        try {
            this.requestCount++;
            const requestId = this.requestCount;
            
            console.log(`🔄 [${requestId}] 开始生成mtgsig签名`);
            console.log(`📦 [${requestId}] 请求参数:`, { url, method, data });
            
            if (!window.H5guard) {
                throw new Error('H5guard未加载');
            }
            
            const signedRequest = await window.H5guard.sign({
                url: url,
                method: method.toUpperCase(),
                data: data
            });
            
            const result = {
                success: true,
                mtgsig: signedRequest.headers.mtgsig,
                url: signedRequest.url,
                method: signedRequest.method,
                data: signedRequest.data,
                headers: signedRequest.headers,
                timestamp: Date.now(),
                requestId: requestId
            };
            
            console.log(`✅ [${requestId}] mtgsig生成成功:`, signedRequest.headers.mtgsig);
            return result;
            
        } catch (error) {
            console.error(`❌ mtgsig生成失败:`, error);
            return {
                success: false,
                error: error.message,
                timestamp: Date.now()
            };
        }
    }
    
    async sendSMSCode(mobile, countrycode = '86') {
        console.log(`📱 生成发送验证码的签名: ${mobile}`);
        return await this.generateMtgsig(
            '/s/gateway/login/h5/login/sendLoginFreeSmsCode',
            'POST',
            { mobile, countrycode }
        );
    }
    
    async testSignature() {
        console.log('🧪 开始签名测试...');
        
        const testCases = [
            {
                name: '发送验证码',
                url: '/s/gateway/login/h5/login/sendLoginFreeSmsCode',
                method: 'POST',
                data: { mobile: '13800138000', countrycode: '86' }
            },
            {
                name: '用户信息',
                url: '/s/gateway/user/h5/userInfo',
                method: 'GET',
                data: {}
            }
        ];
        
        const results = [];
        for (const testCase of testCases) {
            console.log(`\n🔬 测试: ${testCase.name}`);
            const result = await this.generateMtgsig(testCase.url, testCase.method, testCase.data);
            results.push({ ...testCase, result });
        }
        
        console.log('\n📊 测试完成，结果汇总:');
        results.forEach((test, index) => {
            console.log(`${index + 1}. ${test.name}: ${test.result.success ? '✅ 成功' : '❌ 失败'}`);
            if (test.result.success) {
                console.log(`   mtgsig: ${test.result.mtgsig}`);
            }
        });
        
        return results;
    }
    
    async getMtgsig(url, method, data = {}) {
        const result = await this.generateMtgsig(url, method, data);
        if (result.success) {
            return result.mtgsig;
        } else {
            throw new Error(result.error);
        }
    }
}

// 主启动函数
async function startMeituanRPC() {
    try {
        console.log('🚀 正在启动美团RPC服务...');
        
        // 等待H5guard加载
        await waitForH5guard();
        console.log('✅ H5guard已加载');
        
        // 启动RPC服务
        const rpcService = new MeituanRealRPC();
        
        console.log('\n🎉 美团RPC服务启动成功！');
        console.log('\n📖 使用示例:');
        console.log('// 发送验证码签名');
        console.log('await window.MeituanRPC.sendSMSCode("13800138000", "86");');
        console.log('\n// 通用签名生成');
        console.log('await getMtgsig("/api/path", "POST", {data: "value"});');
        console.log('\n// 运行测试');
        console.log('await window.MeituanRPC.testSignature();');
        
        return rpcService;
        
    } catch (error) {
        console.error('❌ RPC服务启动失败:', error);
        console.log('💡 请确保在美团页面中运行此代码');
        throw error;
    }
}

// 自动启动服务
startMeituanRPC().then(() => {
    console.log('\n🔥 服务已就绪，可以开始使用！');
}).catch(error => {
    console.error('启动失败:', error);
});

// 导出给全局使用
window.startMeituanRPC = startMeituanRPC;
window.waitForH5guard = waitForH5guard;
