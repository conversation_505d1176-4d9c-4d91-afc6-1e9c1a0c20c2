/**
 * 美团RPC浏览器客户端
 * 在美团页面控制台中运行此代码，连接到API服务
 */

class MeituanBrowserClient {
    constructor(apiBaseUrl = 'http://localhost:3000') {
        this.apiBaseUrl = apiBaseUrl;
        this.isRunning = false;
        this.pollInterval = null;
        this.heartbeatInterval = null;
        this.init();
    }

    async init() {
        try {
            // 等待H5guard加载
            await this.waitForH5guard();
            console.log('✅ H5guard已加载');

            // 启动客户端
            this.start();
            
            console.log('🎉 美团RPC浏览器客户端已启动！');
            console.log(`🔗 连接到API服务: ${this.apiBaseUrl}`);
            
        } catch (error) {
            console.error('❌ 客户端启动失败:', error);
        }
    }

    // 等待H5guard加载
    waitForH5guard(timeout = 10000) {
        return new Promise((resolve, reject) => {
            if (window.H5guard) {
                resolve(window.H5guard);
                return;
            }

            const startTime = Date.now();
            const checkInterval = setInterval(() => {
                if (window.H5guard) {
                    clearInterval(checkInterval);
                    resolve(window.H5guard);
                } else if (Date.now() - startTime > timeout) {
                    clearInterval(checkInterval);
                    reject(new Error('H5guard加载超时'));
                }
            }, 100);
        });
    }

    // 启动客户端
    start() {
        if (this.isRunning) return;
        
        this.isRunning = true;
        
        // 开始轮询待处理请求
        this.pollInterval = setInterval(() => {
            this.pollPendingRequests();
        }, 1000);

        // 开始发送心跳
        this.heartbeatInterval = setInterval(() => {
            this.sendHeartbeat();
        }, 30000);

        // 立即发送一次心跳
        this.sendHeartbeat();
        
        console.log('🔄 开始轮询API服务请求...');
    }

    // 停止客户端
    stop() {
        this.isRunning = false;
        
        if (this.pollInterval) {
            clearInterval(this.pollInterval);
            this.pollInterval = null;
        }
        
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
        
        console.log('⏹️ 浏览器客户端已停止');
    }

    // 轮询待处理请求
    async pollPendingRequests() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/api/pending-requests`);
            const { requests } = await response.json();
            
            // 处理每个待处理请求
            for (const request of requests) {
                this.processRequest(request);
            }
            
        } catch (error) {
            console.error('轮询请求失败:', error);
        }
    }

    // 处理单个请求
    async processRequest(request) {
        const { requestId, url, method, data } = request;
        
        try {
            console.log(`🔄 处理签名请求 [${requestId}]: ${method} ${url}`);
            
            // 使用H5guard生成签名
            const signedRequest = await window.H5guard.sign({
                url: url,
                method: method.toUpperCase(),
                data: data
            });
            
            // 提交结果到API服务
            await this.submitResult(requestId, {
                success: true,
                mtgsig: signedRequest.headers.mtgsig
            });
            
            console.log(`✅ 签名生成成功 [${requestId}]: ${signedRequest.headers.mtgsig.substring(0, 20)}...`);
            
        } catch (error) {
            console.error(`❌ 签名生成失败 [${requestId}]:`, error);
            
            // 提交错误结果
            await this.submitResult(requestId, {
                success: false,
                error: error.message
            });
        }
    }

    // 提交结果到API服务
    async submitResult(requestId, result) {
        try {
            await fetch(`${this.apiBaseUrl}/api/submit-result`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    requestId,
                    ...result
                })
            });
        } catch (error) {
            console.error('提交结果失败:', error);
        }
    }

    // 发送心跳
    async sendHeartbeat() {
        try {
            await fetch(`${this.apiBaseUrl}/api/heartbeat`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
        } catch (error) {
            console.error('心跳发送失败:', error);
        }
    }
}

// 自动启动客户端
const meituanClient = new MeituanBrowserClient();

// 全局控制方法
window.MeituanClient = {
    start: () => meituanClient.start(),
    stop: () => meituanClient.stop(),
    status: () => ({
        running: meituanClient.isRunning,
        apiUrl: meituanClient.apiBaseUrl
    })
};

console.log('📋 可用控制方法:');
console.log('  - window.MeituanClient.start() // 启动客户端');
console.log('  - window.MeituanClient.stop()  // 停止客户端');
console.log('  - window.MeituanClient.status() // 查看状态');
