# 美团RPC签名生成API服务

将美团RPC签名生成功能封装为HTTP API接口，支持调用历史记录和统计功能。

## 架构说明

- **API服务端**: 提供HTTP接口，管理调用历史和统计
- **浏览器客户端**: 在美团页面注入，负责与H5guard交互生成签名
- **通信方式**: HTTP轮询 + 心跳检测

## 快速开始

### 1. 启动API服务

```bash
# 安装依赖
npm install

# 启动服务
npm start

# 开发模式（自动重启）
npm run dev
```

服务将在 `http://localhost:3000` 启动

### 2. 注入浏览器客户端

1. 打开美团页面: https://sqt.meituan.com/
2. 打开浏览器控制台 (F12)
3. 复制 `browser-client.js` 内容并粘贴运行

### 3. 使用API接口

```bash
# 生成签名
curl -X POST http://localhost:3000/api/generate-signature \
  -H "Content-Type: application/json" \
  -d '{
    "url": "/api/test",
    "method": "POST", 
    "data": {"key": "value"}
  }'

# 查看调用历史
curl http://localhost:3000/api/history

# 查看服务状态
curl http://localhost:3000/api/status
```

## API接口文档

### 1. 生成签名
- **接口**: `POST /api/generate-signature`
- **参数**:
  ```json
  {
    "url": "/api/path",
    "method": "POST",
    "data": {"key": "value"}
  }
  ```
- **响应**:
  ```json
  {
    "success": true,
    "mtgsig": "签名字符串",
    "requestId": "请求ID",
    "timestamp": 1234567890
  }
  ```

### 2. 获取调用历史
- **接口**: `GET /api/history?limit=10`
- **响应**:
  ```json
  {
    "history": [...],
    "statistics": {
      "totalCalls": 100,
      "successCalls": 95,
      "failedCalls": 5,
      "averageResponseTime": 150
    },
    "totalRecords": 100,
    "browserConnected": true
  }
  ```

### 3. 清空调用历史
- **接口**: `DELETE /api/history`

### 4. 获取服务状态
- **接口**: `GET /api/status`

## 使用示例

### Node.js 调用示例

```javascript
const axios = require('axios');

async function generateSignature(url, method, data) {
  try {
    const response = await axios.post('http://localhost:3000/api/generate-signature', {
      url, method, data
    });
    return response.data.mtgsig;
  } catch (error) {
    console.error('签名生成失败:', error.response?.data || error.message);
    throw error;
  }
}

// 使用示例
generateSignature('/api/test', 'POST', {key: 'value'})
  .then(mtgsig => console.log('签名:', mtgsig))
  .catch(console.error);
```

### Python 调用示例

```python
import requests

def generate_signature(url, method, data=None):
    response = requests.post('http://localhost:3000/api/generate-signature', json={
        'url': url, 'method': method, 'data': data or {}
    })
    
    if response.status_code == 200:
        return response.json()['mtgsig']
    else:
        raise Exception(f"签名生成失败: {response.text}")

# 使用示例
mtgsig = generate_signature('/api/test', 'POST', {'key': 'value'})
print(f"签名: {mtgsig}")
```

## 特性

- ✅ HTTP API接口，易于集成
- ✅ 自动调用历史记录和统计
- ✅ 浏览器客户端自动重连
- ✅ 请求超时处理
- ✅ 心跳检测机制
- ✅ 内存管理（最多保存1000条记录）
- ✅ 跨域支持
