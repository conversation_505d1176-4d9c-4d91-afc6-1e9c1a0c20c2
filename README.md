# 美团RPC签名生成服务

## 项目简介

本项目实现了美团H5guard签名机制的RPC调用服务，支持真实的mtgsig签名生成，主要用于发送验证码和通用API签名。

## 核心功能

- ✅ **真实mtgsig签名生成** - 使用美团官方H5guard.sign方法
- ✅ **发送验证码API支持** - 专门优化的验证码发送签名
- ✅ **通用API签名支持** - 支持任意美团API的签名生成
- ✅ **完整RPC服务** - 标准化的调用接口和错误处理
- ✅ **Python客户端** - 便于自动化和批量处理

## 快速开始

### 1. 浏览器端设置

1. 打开美团页面: https://sqt.meituan.com/
2. 打开浏览器控制台 (F12)
3. 复制 `meituan_rpc_service.js` 中的代码并运行

### 2. 测试签名生成

```javascript
// 发送验证码签名
await window.MeituanRPC.sendSMSCode('13800138000', '86');

// 通用签名生成
await getMtgsig('/s/gateway/user/h5/userInfo', 'GET', {});

// 运行完整测试
await window.MeituanRPC.testSignature();
```

### 3. Python客户端测试

```bash
# 运行Python测试客户端
python real_client.py
```

## 文件说明

- `meituan_rpc_service.js` - 浏览器端RPC服务代码
- `real_client.py` - Python测试客户端
- `docs/美团RPC签名生成服务实验报告.md` - 详细实验报告
- `README.md` - 本说明文档

## 使用示例

### JavaScript调用

```javascript
// 快速获取mtgsig
const mtgsig = await getMtgsig('/api/path', 'POST', {data: 'value'});

// 发送验证码专用
const smsResult = await window.MeituanRPC.sendSMSCode('手机号', '86');

// 完整签名信息
const fullResult = await window.MeituanRPC.generateMtgsig(
    '/s/gateway/login/h5/login/sendLoginFreeSmsCode',
    'POST',
    { mobile: '13800138000', countrycode: '86' }
);
```

### Python调用

```python
from real_client import MeituanRPCTestClient

# 创建客户端
client = MeituanRPCTestClient()

# 生成发送验证码签名
sms_result = client.send_sms_code_signature('13800138000', '86')

# 生成通用签名
mtgsig = client.get_mtgsig_only('/api/path', 'POST', {'data': 'value'})
```

## 技术特点

### 优势
- 真实签名生成，无需逆向算法
- 完整的错误处理和日志记录
- 支持多种调用方式
- 易于集成和扩展

### 限制
- 依赖浏览器环境
- 需要在美团页面中运行
- 受浏览器同源策略限制

## 实验结果

经过完整测试验证：

- ✅ H5guard加载检测正常
- ✅ 发送验证码签名生成成功
- ✅ 通用API签名生成成功
- ✅ RPC服务封装完整
- ✅ Python客户端集成正常

## 注意事项

1. 必须在美团页面 (https://sqt.meituan.com/) 中运行
2. 确保H5guard已正确加载
3. 生成的签名具有时效性
4. 请遵守美团的使用条款

## 许可证

本项目仅供学习和研究使用，请勿用于商业用途。

---

**最后更新**: 2025-01-02  
**状态**: ✅ 实验成功
