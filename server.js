/**
 * 美团RPC签名生成API服务
 * 提供HTTP接口，管理签名生成和调用历史
 */

const express = require('express');
const cors = require('cors');
const { v4: uuidv4 } = require('uuid');

const app = express();
const PORT = process.env.PORT || 3000;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.static('public')); // 静态文件服务

// 全局状态管理
class MeituanRPCService {
    constructor() {
        this.callHistory = [];
        this.statistics = {
            totalCalls: 0,
            successCalls: 0,
            failedCalls: 0,
            averageResponseTime: 0,
            startTime: Date.now()
        };
        this.pendingRequests = new Map(); // 待处理的请求
        this.browserConnected = false;
        this.lastHeartbeat = null;
    }

    // 添加调用记录
    addCallRecord(record) {
        this.callHistory.push(record);
        
        // 保持最近1000条记录
        if (this.callHistory.length > 1000) {
            this.callHistory = this.callHistory.slice(-1000);
        }
        
        // 更新统计信息
        this.statistics.totalCalls++;
        if (record.success) {
            this.statistics.successCalls++;
        } else {
            this.statistics.failedCalls++;
        }
        
        // 更新平均响应时间
        const totalResponseTime = this.callHistory.reduce((sum, call) => sum + call.responseTime, 0);
        this.statistics.averageResponseTime = Math.round(totalResponseTime / this.callHistory.length);
    }

    // 获取调用历史
    getHistory(limit = 10) {
        return {
            history: this.callHistory.slice(-limit).reverse(),
            statistics: this.statistics,
            totalRecords: this.callHistory.length,
            browserConnected: this.browserConnected,
            lastHeartbeat: this.lastHeartbeat
        };
    }

    // 清空历史记录
    clearHistory() {
        const oldCount = this.callHistory.length;
        this.callHistory = [];
        this.statistics = {
            totalCalls: 0,
            successCalls: 0,
            failedCalls: 0,
            averageResponseTime: 0,
            startTime: Date.now()
        };
        return { cleared: oldCount };
    }
}

const rpcService = new MeituanRPCService();

// API路由

// 1. 生成签名接口
app.post('/api/generate-signature', async (req, res) => {
    try {
        const { url, method, data = {} } = req.body;
        
        if (!url || !method) {
            return res.status(400).json({
                success: false,
                error: '缺少必要参数: url, method'
            });
        }

        if (!rpcService.browserConnected) {
            return res.status(503).json({
                success: false,
                error: '浏览器客户端未连接，请先在美团页面注入JS代码'
            });
        }

        const requestId = uuidv4();
        const startTime = Date.now();

        // 创建待处理请求
        const requestPromise = new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                rpcService.pendingRequests.delete(requestId);
                reject(new Error('请求超时'));
            }, 30000); // 30秒超时

            rpcService.pendingRequests.set(requestId, {
                resolve,
                reject,
                timeout,
                startTime,
                url,
                method,
                data
            });
        });

        // 通知浏览器端生成签名（通过轮询接口）
        console.log(`📤 新签名请求 [${requestId}]: ${method} ${url}`);

        // 等待浏览器端响应
        const result = await requestPromise;
        
        // 记录调用历史
        const responseTime = Date.now() - startTime;
        rpcService.addCallRecord({
            requestId,
            url,
            method,
            data,
            success: result.success,
            mtgsig: result.mtgsig,
            responseTime,
            timestamp: Date.now(),
            error: result.error || null
        });

        res.json(result);

    } catch (error) {
        console.error('签名生成失败:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 2. 浏览器端轮询待处理请求
app.get('/api/pending-requests', (req, res) => {
    const requests = Array.from(rpcService.pendingRequests.entries()).map(([id, req]) => ({
        requestId: id,
        url: req.url,
        method: req.method,
        data: req.data,
        timestamp: req.startTime
    }));

    res.json({ requests });
});

// 3. 浏览器端提交签名结果
app.post('/api/submit-result', (req, res) => {
    const { requestId, success, mtgsig, error } = req.body;
    
    const pendingRequest = rpcService.pendingRequests.get(requestId);
    if (!pendingRequest) {
        return res.status(404).json({ error: '请求不存在或已过期' });
    }

    // 清理超时定时器
    clearTimeout(pendingRequest.timeout);
    rpcService.pendingRequests.delete(requestId);

    // 解析Promise
    const result = {
        success,
        mtgsig,
        error,
        requestId,
        timestamp: Date.now()
    };

    pendingRequest.resolve(result);
    
    console.log(`📥 收到签名结果 [${requestId}]: ${success ? '成功' : '失败'}`);
    res.json({ received: true });
});

// 4. 浏览器端心跳接口
app.post('/api/heartbeat', (req, res) => {
    rpcService.browserConnected = true;
    rpcService.lastHeartbeat = Date.now();
    res.json({ status: 'ok' });
});

// 5. 获取调用历史
app.get('/api/history', (req, res) => {
    const limit = parseInt(req.query.limit) || 10;
    res.json(rpcService.getHistory(limit));
});

// 6. 清空调用历史
app.delete('/api/history', (req, res) => {
    const result = rpcService.clearHistory();
    res.json({ success: true, ...result });
});

// 7. 获取服务状态
app.get('/api/status', (req, res) => {
    res.json({
        connected: rpcService.browserConnected,
        lastHeartbeat: rpcService.lastHeartbeat,
        pendingRequests: rpcService.pendingRequests.size,
        statistics: rpcService.statistics,
        uptime: Date.now() - rpcService.statistics.startTime
    });
});

// 检查浏览器连接状态
setInterval(() => {
    if (rpcService.lastHeartbeat && Date.now() - rpcService.lastHeartbeat > 60000) {
        rpcService.browserConnected = false;
        console.log('⚠️ 浏览器客户端连接超时');
    }
}, 30000);

// 启动服务
app.listen(PORT, () => {
    console.log(`🚀 美团RPC API服务已启动`);
    console.log(`📡 服务地址: http://localhost:${PORT}`);
    console.log(`📋 API文档:`);
    console.log(`  POST /api/generate-signature - 生成签名`);
    console.log(`  GET  /api/history - 获取调用历史`);
    console.log(`  GET  /api/status - 获取服务状态`);
    console.log(`\n💡 请在美团页面注入浏览器客户端代码以开始使用`);
});
